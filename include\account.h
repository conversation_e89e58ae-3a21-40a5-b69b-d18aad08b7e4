#ifndef ACCOUNT_H
#define ACCOUNT_H
#include "date.h"
#include "accountrecord.h"
#include <string>
#include <map>
#include <vector>
#include <stdexcept>
using namespace std;
class Account;// 前向声明
class AccountException : public std::runtime_error {// 账户异常类
private:
    const Account* account;
public:
    AccountException(const Account* acc, const std::string& msg)
        : std::runtime_error(msg), account(acc) {}
    const Account* getAccount() const { return account; }
};
class Account {// 账户基类
private:
    string id;      // 账号
    double balance;      // 余额
    static double total; // 所有账户的总金额
    static multimap<Date, AccountRecord> recordMap; // 所有账户的账目记录
protected:
    Account(const Date& date, const std::string& id);
    void record(const Date& date, double amount, const std::string& desc);
    static double getStaticTotal(); // 获取静态total值
    static void setStaticTotal(double newTotal); // 设置静态total值
public:
    string getId() const;
    double getBalance() const;
    virtual void deposit(const Date& date, double amount, const std::string& desc) = 0;
    virtual void withdraw(const Date& date, double amount, const std::string& desc) = 0;
    virtual void settle(const Date& date) = 0;
    virtual void show() const;
    static double getTotal();
    static void query(const Date& start, const Date& end);    // 查询指定日期范围内的账目记录  
    static void queryByTime(const Date& start, const Date& end);  // 按时间排序查询
    static void queryByAmount(const Date& start, const Date& end);    // 按金额排序查询（从大到小）
};
#endif