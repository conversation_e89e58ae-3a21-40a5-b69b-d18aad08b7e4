#include "bankinterface.h"
#include "savingsaccount.h"
#include "creditaccount.h"
#include <iostream>
#include <iomanip>
#include <limits>
#include <memory>

#ifdef _WIN32
#include <cstdlib>
#define CLEAR_SCREEN "cls"
#else
#define <PERSON><PERSON>AR_SCREEN "clear"
#endif

using namespace std;

BankInterface::BankInterface() : currentDate(2024, 1, 1) {
}

void BankInterface::run() {
    cout << "=== 欢迎使用个人银行账户管理系统 ===" << endl;

    while (true) {
        if (!userManager.isLoggedIn()) {
            showLoginMenu();
        } else {
            showUserReminders();
            showMainMenu();
        }
    }
}

void BankInterface::showLoginMenu() {
    clearScreen();
    cout << "\n=== 登录/注册 ===" << endl;
    cout << "1. 登录" << endl;
    cout << "2. 注册" << endl;
    cout << "3. 退出系统" << endl;
    cout << "请选择: ";

    int choice;
    cin >> choice;

    switch (choice) {
        case 1:
            if (!handleLogin()) {
                cout << "登录失败！用户名或密码错误。" << endl;
                pauseScreen();
            }
            break;
        case 2:
            handleRegister();
            break;
        case 3:
            cout << "感谢使用！再见！" << endl;
            exit(0);
        default:
            cout << "无效选择！" << endl;
            pauseScreen();
    }
}

bool BankInterface::handleLogin() {
    string username, password;
    cout << "用户名: ";
    cin >> username;
    cout << "密码: ";
    cin >> password;

    return userManager.loginUser(username, password);
}

bool BankInterface::handleRegister() {
    string username, password, confirmPassword;

    cout << "用户名: ";
    cin >> username;

    cout << "密码 (6-50字符): ";
    cin >> password;

    cout << "确认密码: ";
    cin >> confirmPassword;

    if (password != confirmPassword) {
        cout << "两次输入的密码不一致！" << endl;
        pauseScreen();
        return false;
    }

    bool result = userManager.registerUser(username, password);
    if (result) {
        cout << "注册成功！请登录。" << endl;
    } else {
        cout << "注册失败！用户名已存在或格式不正确。" << endl;
        cout << "用户名要求：3-20字符，仅字母数字下划线" << endl;
        cout << "密码要求：6-50字符" << endl;
    }
    pauseScreen();
    return result;
}

void BankInterface::showMainMenu() {
    clearScreen();
    auto user = userManager.getCurrentUser();
    cout << "\n=== 欢迎, " << user->getUsername() << "! ===" << endl;

    currentDate.show();
    cout << "\t总资产: " << user->getTotalBalance() << endl;

    cout << "\n=== 主菜单 ===" << endl;
    cout << "1. 创建账户" << endl;
    cout << "2. 存款" << endl;
    cout << "3. 取款" << endl;
    cout << "4. 显示账户" << endl;
    cout << "5. 账户统计" << endl;
    cout << "6. 查询记录" << endl;
    cout << "7. 按时间排序查询" << endl;
    cout << "8. 按金额排序查询" << endl;
    cout << "9. 更改日期" << endl;
    cout << "10. 下个月" << endl;
    cout << "11. 月度统计" << endl;
    cout << "0. 退出登录" << endl;
    cout << "请选择: ";

    handleMainMenu();
}

void BankInterface::handleMainMenu() {
    int choice;
    cin >> choice;

    try {
        switch (choice) {
            case 1: createAccount(); break;
            case 2: depositMoney(); break;
            case 3: withdrawMoney(); break;
            case 4: showAccounts(); break;
            case 5: showAccountStatistics(); break;
            case 6: queryRecords(); break;
            case 7: queryRecordsByTime(); break;
            case 8: queryRecordsByAmount(); break;
            case 9: changeDate(); break;
            case 10: nextMonth(); break;
            case 11: showMonthlyStatistics(); break;
            case 0:
                userManager.logoutUser();
                cout << "已退出登录！" << endl;
                pauseScreen();
                return;
            default:
                cout << "无效选择！" << endl;
                pauseScreen();
        }
    } catch (const exception& e) {
        handleException(e);
    }
}

void BankInterface::clearScreen() {
    system(CLEAR_SCREEN);
}

void BankInterface::pauseScreen() {
    cout << "按回车键继续...";
    cin.ignore(numeric_limits<streamsize>::max(), '\n');
    cin.get();
}

void BankInterface::handleException(const exception& e) {
    cout << "错误: " << e.what() << endl;

    // 如果是账户异常，显示账户信息
    const AccountException* accEx = dynamic_cast<const AccountException*>(&e);
    if (accEx && accEx->getAccount()) {
        cout << "相关账户: ";
        accEx->getAccount()->show();
        cout << endl;
    }

    pauseScreen();
}

void BankInterface::createAccount() {
    auto user = userManager.getCurrentUser();

    cout << "\n=== 创建账户 ===" << endl;
    cout << "1. 储蓄账户" << endl;
    cout << "2. 信用账户" << endl;
    cout << "选择账户类型: ";

    int type;
    cin >> type;

    string id;
    cout << "账户ID: ";
    cin >> id;

    shared_ptr<Account> account;

    if (type == 1) {
        double rate;
        cout << "年利率 (例如: 0.05): ";
        cin >> rate;
        account = make_shared<SavingsAccount>(currentDate, id, rate);
    } else if (type == 2) {
        double credit, rate, fee;
        cout << "信用额度: ";
        cin >> credit;
        cout << "日利率 (例如: 0.0005): ";
        cin >> rate;
        cout << "年费: ";
        cin >> fee;
        account = make_shared<CreditAccount>(currentDate, id, credit, rate, fee);
    } else {
        cout << "无效的账户类型！" << endl;
        pauseScreen();
        return;
    }

    user->addAccount(account);
    cout << "账户创建成功！" << endl;
    pauseScreen();
}

void BankInterface::depositMoney() {
    auto user = userManager.getCurrentUser();

    if (user->getAccountCount() == 0) {
        cout << "您还没有账户，请先创建账户！" << endl;
        pauseScreen();
        return;
    }

    cout << "\n=== 存款 ===" << endl;
    showAccounts();

    cout << "选择账户 (0-" << user->getAccountCount() - 1 << "): ";
    size_t index;
    cin >> index;

    auto account = user->getAccount(index);
    if (!account) {
        cout << "无效的账户索引！" << endl;
        pauseScreen();
        return;
    }

    double amount;
    string desc;
    cout << "存款金额: ";
    cin >> amount;
    cout << "描述: ";
    cin.ignore();
    getline(cin, desc);

    account->deposit(currentDate, amount, desc);
    cout << "存款成功！" << endl;
    pauseScreen();
}

void BankInterface::withdrawMoney() {
    auto user = userManager.getCurrentUser();

    if (user->getAccountCount() == 0) {
        cout << "您还没有账户，请先创建账户！" << endl;
        pauseScreen();
        return;
    }

    cout << "\n=== 取款 ===" << endl;
    showAccounts();

    cout << "选择账户 (0-" << user->getAccountCount() - 1 << "): ";
    size_t index;
    cin >> index;

    auto account = user->getAccount(index);
    if (!account) {
        cout << "无效的账户索引！" << endl;
        pauseScreen();
        return;
    }

    double amount;
    string desc;
    cout << "取款金额: ";
    cin >> amount;
    cout << "描述: ";
    cin.ignore();
    getline(cin, desc);

    account->withdraw(currentDate, amount, desc);
    cout << "取款成功！" << endl;
    pauseScreen();
}

void BankInterface::showAccounts() {
    auto user = userManager.getCurrentUser();

    cout << "\n=== 账户列表 ===" << endl;
    for (size_t i = 0; i < user->getAccountCount(); i++) {
        cout << "[" << i << "] ";
        user->getAccount(i)->show();
        cout << endl;
    }
}

void BankInterface::showAccountStatistics() {
    auto user = userManager.getCurrentUser();

    cout << "\n=== 账户统计 ===" << endl;
    cout << "总账户数: " << user->getAccountCount() << endl;
    cout << "总资产: " << user->getTotalBalance() << endl;

    double totalSavings = 0, totalCredit = 0;
    for (size_t i = 0; i < user->getAccountCount(); i++) {
        auto account = user->getAccount(i);
        auto creditAccount = dynamic_pointer_cast<CreditAccount>(account);
        if (creditAccount) {
            totalCredit += account->getBalance();
        } else {
            totalSavings += account->getBalance();
        }
    }

    cout << "储蓄账户总额: " << totalSavings << endl;
    cout << "信用账户总额: " << totalCredit << endl;
    pauseScreen();
}

void BankInterface::queryRecords() {
    cout << "\n=== 查询记录 ===" << endl;
    cout << "开始日期 (YYYY/MM/DD): ";
    Date startDate = Date::read();
    cout << "结束日期 (YYYY/MM/DD): ";
    Date endDate = Date::read();

    Account::query(startDate, endDate);
    pauseScreen();
}

void BankInterface::queryRecordsByTime() {
    cout << "\n=== 按时间排序查询 ===" << endl;
    cout << "开始日期 (YYYY/MM/DD): ";
    Date startDate = Date::read();
    cout << "结束日期 (YYYY/MM/DD): ";
    Date endDate = Date::read();

    Account::queryByTime(startDate, endDate);
    pauseScreen();
}

void BankInterface::queryRecordsByAmount() {
    cout << "\n=== 按金额排序查询 ===" << endl;
    cout << "开始日期 (YYYY/MM/DD): ";
    Date startDate = Date::read();
    cout << "结束日期 (YYYY/MM/DD): ";
    Date endDate = Date::read();

    Account::queryByAmount(startDate, endDate);
    pauseScreen();
}

void BankInterface::changeDate() {
    cout << "\n=== 更改日期 ===" << endl;
    cout << "当前日期: ";
    currentDate.show();
    cout << endl;

    cout << "新日期 (YYYY/MM/DD): ";
    Date newDate = Date::read();

    if (newDate < currentDate) {
        cout << "不能设置过去的日期！" << endl;
    } else {
        currentDate = newDate;
        cout << "日期已更新！" << endl;
    }
    pauseScreen();
}

void BankInterface::nextMonth() {
    cout << "\n=== 进入下个月 ===" << endl;

    if (currentDate.getMonth() == 12) {
        currentDate = Date(currentDate.getYear() + 1, 1, 1);
    } else {
        currentDate = Date(currentDate.getYear(), currentDate.getMonth() + 1, 1);
    }

    // 对所有用户账户进行结算
    auto user = userManager.getCurrentUser();
    for (size_t i = 0; i < user->getAccountCount(); i++) {
        user->getAccount(i)->settle(currentDate);
    }

    cout << "已进入下个月，所有账户已结算！" << endl;
    cout << "当前日期: ";
    currentDate.show();
    cout << endl;
    pauseScreen();
}

void BankInterface::showUserReminders() {
    auto user = userManager.getCurrentUser();
    auto reminders = user->getReminders();

    if (!reminders.empty()) {
        cout << "\n=== 重要提醒 ===" << endl;
        for (const auto& reminder : reminders) {
            cout << "• " << reminder << endl;
        }
        cout << endl;
    }
}

void BankInterface::showMonthlyStatistics() {
    auto user = userManager.getCurrentUser();

    cout << "\n=== 月度统计 ===" << endl;
    cout << "当前月份: " << currentDate.getYear() << "年" << currentDate.getMonth() << "月" << endl;

    double income = user->getMonthlyIncome(currentDate.getYear(), currentDate.getMonth());
    double expense = user->getMonthlyExpense(currentDate.getYear(), currentDate.getMonth());

    cout << "本月收入: " << income << endl;
    cout << "本月支出: " << expense << endl;
    cout << "净收入: " << (income - expense) << endl;
    cout << "当前总资产: " << user->getTotalBalance() << endl;

    pauseScreen();
}

